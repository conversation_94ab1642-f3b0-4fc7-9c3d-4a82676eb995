import { defineStore } from 'pinia';

export const useAppStore = defineStore('app', {
  state: () => ({
    promptElements: {},
    mainPrompt: '',
    prompt: '',
    dataFolder: '',
    categories: [],
    checkboxes: [],
    usedItems: new Set(),
    isLoading: true,
    savedPrompts: []
  }),

  actions: {
    appendToMainPrompt(text) {
      if (!text) return;
      if (this.mainPrompt.length > 0) {
        this.mainPrompt += '\n' + text;
      } else {
        this.mainPrompt = text;
      }
    },

    setPrompt(text) {
      this.prompt = text;
    },

    appendToPrompt(text, key) {
      if (text) {
        this.prompt += (this.prompt ? '\n\n' : '') + text;
        this.usedItems.add(key);
      }
    },

    clearPrompt() {
      this.prompt = '';
      this.usedItems.clear();
      this.categories.forEach(cat => {
        cat.selected = '';
      });
    },

    async fetchData() {
      this.isLoading = true;
      try {
        const [categoriesRes, checkboxesRes, promptElementsRes] = await Promise.all([
          fetch('/categories.json').catch(e => e),
          fetch('/checkboxes.json').catch(e => e),
          fetch('/prompt_elements.json').catch(e => e)
        ]);

        if (categoriesRes.ok) {
          const categoriesData = await categoriesRes.json();
          this.categories = categoriesData.map(cat => ({ ...cat, selected: '' }));
        }

        if (checkboxesRes.ok) {
          this.checkboxes = await checkboxesRes.json();
        }

        if (promptElementsRes.ok) {
          this.promptElements = await promptElementsRes.json();
        }

        if (window.electronAPI && window.electronAPI.loadData) {
          const data = await window.electronAPI.loadData();
          if (data) {
            this.savedPrompts = data.prompts || [];
            // Assign items to categories
            for (const category of this.categories) {
              if (data[category.id] && data[category.id].items) {
                category.items = data[category.id].items;
              }
            }
          }
        } else {
          console.warn('window.electronAPI is not available. Running in browser mode.');
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async savePrompt() {
      if (!this.prompt.trim()) return false;
      try {
        if (window.electronAPI && window.electronAPI.savePrompt) {
          return await window.electronAPI.savePrompt(this.prompt);
        }
        console.warn('Mocking prompt save.');
        return true;
      } catch (error) {
        console.error('Error saving prompt:', error);
        return false;
      }
    },

    async copyToClipboard() {
      if (!this.prompt) return false;
      try {
        await navigator.clipboard.writeText(this.prompt);
        return true;
      } catch (error) {
        console.error('Error copying to clipboard:', error);
        return false;
      }
    },

    setDataFolder(folder) {
      this.dataFolder = folder;
      this.fetchData();
    },

    isItemUsed(key) {
      return this.usedItems.has(key);
    },

    // New actions for MainPanel functionality
    clearMainPrompt() {
      this.mainPrompt = '';
    },

    async saveMainPromptToList() {
      if (!this.mainPrompt.trim()) return false;

      const newPrompt = {
        id: Date.now(),
        content: this.mainPrompt.trim(),
        createdAt: new Date().toISOString(),
        preview: this.mainPrompt.trim().substring(0, 100) + (this.mainPrompt.trim().length > 100 ? '...' : '')
      };

      this.savedPrompts.unshift(newPrompt); // Add to beginning of array

      // Also save to file system if available
      try {
        if (window.electronAPI && window.electronAPI.savePrompt) {
          await window.electronAPI.savePrompt(this.mainPrompt);
        }
        return true;
      } catch (error) {
        console.error('Error saving prompt to file:', error);
        return true; // Still return true since we saved to local state
      }
    },

    loadPromptFromSaved(promptId) {
      const savedPrompt = this.savedPrompts.find(p => p.id === promptId);
      if (savedPrompt) {
        this.mainPrompt = savedPrompt.content;
        return true;
      }
      return false;
    },

    removeSavedPrompt(promptId) {
      const index = this.savedPrompts.findIndex(p => p.id === promptId);
      if (index !== -1) {
        this.savedPrompts.splice(index, 1);
        return true;
      }
      return false;
    }
  }
});
